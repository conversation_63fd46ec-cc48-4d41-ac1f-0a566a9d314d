import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  Box,
  TextField,
  InputAdornment,
  IconButton,
  Badge,
  Tabs,
  Tab,
} from "@mui/material";
import {
  Search as SearchIcon,
  Notifications as NotificationsIcon,
} from "@mui/icons-material";
import { useState } from "react";

function Header() {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  return (
    <AppBar position="static" color="default" elevation={1}>
      <Toolbar
        sx={{
          justifyContent: "space-between",
          minHeight: "64px",
          bgcolor: "#fff",
          borderBottom: 1,
          borderColor: "divider",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Text" />
            <Tab label="Text" />
            <Tab label="Text" />
          </Tabs>
        </Box>

        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <TextField
            size="small"
            placeholder="Search"
            variant="outlined"
            sx={{ width: 200 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
            }}
          />

          <Typography variant="body2" color="text.secondary">
            Text Text Text Text
          </Typography>

          <IconButton color="inherit"></IconButton>
        </Box>
      </Toolbar>
    </AppBar>
  );
}

export default Header;
